import React, { useEffect, useRef } from 'react';

interface AnimatedHtmlRendererProps {
  html: string;
  delayPerElement?: number;
  onAnimationComplete?: () => void;
}

interface StreamingHtmlRendererProps {
  stream: ReadableStream<Uint8Array>;
  delayPerElement?: number;
  onAnimationComplete?: () => void;
  onError?: (error: Error) => void;
}

/**
 * AnimatedHtmlRenderer - Provides smooth, jitter-free, progressive element-by-element rendering
 * 
 * This component emulates Readdy.ai's smooth rendering by:
 * 1. Parsing HTML string into individual DOM nodes
 * 2. Rendering each top-level node one at a time with smooth animations
 * 3. Using Tailwind utility classes for fade-in and slide-up effects
 * 4. Avoiding layout jank by using manual DOM manipulation instead of dangerouslySetInnerHTML
 */
const AnimatedHtmlRenderer: React.FC<AnimatedHtmlRendererProps> = ({ 
  html, 
  delayPerElement = 75,
  onAnimationComplete
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<{ cancel: boolean }>({ cancel: false });

  useEffect(() => {
    const container = containerRef.current;
    if (!container || !html.trim()) {
      console.log('🎬 AnimatedHtmlRenderer: No container or empty HTML', {
        hasContainer: !!container,
        htmlLength: html?.length || 0,
        htmlPreview: html?.substring(0, 100) + '...'
      });
      return;
    }

    // Reset animation state
    animationRef.current = { cancel: false };

    // Clear existing content
    container.innerHTML = '';

    console.log('🎬 AnimatedHtmlRenderer: Starting rendering', {
      htmlLength: html.length,
      htmlPreview: html.substring(0, 200) + '...'
    });

    // Parse HTML using DOMParser for safe parsing
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');
    const bodyContent = doc.body;

    if (!bodyContent) {
      console.log('🎬 AnimatedHtmlRenderer: No body content found');
      return;
    }

    // Get all top-level nodes from the parsed HTML
    const topLevelNodes = Array.from(bodyContent.childNodes).filter(node => {
      // Filter out empty text nodes and comments
      return node.nodeType === Node.ELEMENT_NODE ||
             (node.nodeType === Node.TEXT_NODE && node.textContent?.trim());
    });

    console.log(`🎬 AnimatedHtmlRenderer: Starting animated rendering of ${topLevelNodes.length} elements`, {
      nodeTypes: topLevelNodes.map(node => ({
        type: node.nodeType === Node.ELEMENT_NODE ? 'element' : 'text',
        tagName: node.nodeType === Node.ELEMENT_NODE ? (node as Element).tagName : 'TEXT',
        content: node.textContent?.substring(0, 50) + '...'
      }))
    });

    // Animate each top-level node progressively
    const animateNodes = async () => {
      for (let i = 0; i < topLevelNodes.length; i++) {
        // Check if animation was cancelled (component unmounted)
        if (animationRef.current.cancel) {
          console.log('🎬 Animation cancelled');
          return;
        }

        const node = topLevelNodes[i];
        
        // Clone the node to avoid moving it from the original document
        const clonedNode = node.cloneNode(true) as Node;
        
        // If it's an element, add initial animation classes for smooth entry
        if (clonedNode.nodeType === Node.ELEMENT_NODE) {
          const element = clonedNode as HTMLElement;
          
          // Add Tailwind classes for initial state (hidden and slightly offset)
          element.classList.add(
            'opacity-0', 
            'translate-y-2', 
            'transition-opacity', 
            'transition-transform', 
            'duration-300', 
            'ease-out'
          );
        }

        // Append to container (element is still invisible due to opacity-0)
        container.appendChild(clonedNode);

        // Trigger animation on next frame to ensure element is in DOM
        if (clonedNode.nodeType === Node.ELEMENT_NODE) {
          const element = clonedNode as HTMLElement;
          
          // Use requestAnimationFrame to ensure the element is properly rendered
          requestAnimationFrame(() => {
            if (animationRef.current.cancel) return;
            
            // Trigger the fade-in and slide-up animation
            element.classList.remove('opacity-0', 'translate-y-2');
            element.classList.add('opacity-100', 'translate-y-0');
          });
        }

        // Wait before processing next element (creates the progressive effect)
        if (i < topLevelNodes.length - 1) {
          await new Promise(resolve => setTimeout(resolve, delayPerElement));
        }
      }

      console.log('🎬 Animation completed');
      
      // Call completion callback if provided
      if (onAnimationComplete) {
        onAnimationComplete();
      }
    };

    // Start the animation sequence
    animateNodes().catch(error => {
      console.error('🎬 Animation error:', error);
    });

    // Cleanup function to cancel animation if component unmounts
    return () => {
      animationRef.current.cancel = true;
    };
  }, [html, delayPerElement, onAnimationComplete]);

  return (
    <div 
      ref={containerRef} 
      className="animated-html-container w-full h-full"
      style={{ minHeight: '100%' }}
    />
  );
};

/**
 * StreamingHtmlRenderer - Provides smooth, jitter-free, progressive element-by-element rendering from a ReadableStream
 *
 * This component handles streamed HTML content by:
 * 1. Reading from a ReadableStream<Uint8Array> progressively
 * 2. Parsing HTML incrementally and safely, element-by-element
 * 3. Appending each parsed top-level element to the DOM progressively using useRef()
 * 4. Animating each appended element using Tailwind CSS for smooth fade-in and slide-up
 * 5. Avoiding full reflows, layout jitter, and scroll jumps
 */
const StreamingHtmlRenderer: React.FC<StreamingHtmlRendererProps> = ({
  stream,
  delayPerElement = 75,
  onAnimationComplete,
  onError
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<{ cancel: boolean }>({ cancel: false });
  const bufferRef = useRef<string>('');
  const readerRef = useRef<ReadableStreamDefaultReader<Uint8Array> | null>(null);

  useEffect(() => {
    const container = containerRef.current;
    if (!container || !stream) return;

    // Reset animation state
    animationRef.current = { cancel: false };
    bufferRef.current = '';

    // Clear existing content
    container.innerHTML = '';

    console.log('🎬 Starting streaming HTML rendering');

    const processStream = async () => {
      const decoder = new TextDecoder();
      const reader = stream.getReader();
      readerRef.current = reader;

      try {
        while (true) {
          // Check if animation was cancelled
          if (animationRef.current.cancel) {
            console.log('🎬 Streaming animation cancelled');
            await reader.cancel();
            return;
          }

          const { done, value } = await reader.read();

          if (done) {
            console.log('🎬 Stream completed');
            // Process any remaining content in buffer
            await processRemainingBuffer();
            break;
          }

          // Decode the chunk and add to buffer
          const chunk = decoder.decode(value, { stream: true });
          bufferRef.current += chunk;

          // Extract and render complete elements from buffer
          await extractAndRenderElements();
        }

        // Call completion callback if provided
        if (onAnimationComplete && !animationRef.current.cancel) {
          onAnimationComplete();
        }

      } catch (error) {
        console.error('🎬 Streaming error:', error);
        if (onError) {
          onError(error as Error);
        }
      } finally {
        readerRef.current = null;
      }
    };

    // Extract complete HTML elements from buffer and render them
    const extractAndRenderElements = async () => {
      const buffer = bufferRef.current;
      const elementsToRender: string[] = [];
      let processedLength = 0;

      // Use a more sophisticated approach to extract complete elements
      const extractedElements = extractCompleteElements(buffer);

      if (extractedElements.elements.length > 0) {
        elementsToRender.push(...extractedElements.elements);
        processedLength = extractedElements.processedLength;

        // Update buffer to remove processed elements
        bufferRef.current = buffer.substring(processedLength);

        // Render each complete element
        for (const elementHtml of elementsToRender) {
          if (animationRef.current.cancel) return;

          await renderSingleElement(elementHtml);

          // Add delay between elements
          if (delayPerElement > 0) {
            await new Promise(resolve => setTimeout(resolve, delayPerElement));
          }
        }
      }
    };

    // Extract complete HTML elements from a buffer string
    const extractCompleteElements = (buffer: string): { elements: string[], processedLength: number } => {
      const elements: string[] = [];
      let position = 0;

      while (position < buffer.length) {
        // Skip whitespace
        while (position < buffer.length && /\s/.test(buffer[position])) {
          position++;
        }

        if (position >= buffer.length) break;

        // Look for opening tag
        if (buffer[position] === '<') {
          const elementResult = extractSingleElement(buffer, position);

          if (elementResult.element) {
            elements.push(elementResult.element);
            position = elementResult.endPosition;
          } else {
            // Incomplete element, stop processing
            break;
          }
        } else {
          // Text content - extract until next tag or end
          const textStart = position;
          while (position < buffer.length && buffer[position] !== '<') {
            position++;
          }

          const textContent = buffer.substring(textStart, position).trim();
          if (textContent) {
            // Wrap text content in a span for consistent handling
            elements.push(`<span>${textContent}</span>`);
          }
        }
      }

      return { elements, processedLength: position };
    };

    // Extract a single complete HTML element starting at a given position
    const extractSingleElement = (buffer: string, startPos: number): { element: string | null, endPosition: number } => {
      if (buffer[startPos] !== '<') {
        return { element: null, endPosition: startPos };
      }

      // Find the end of the opening tag
      let tagEnd = startPos + 1;
      while (tagEnd < buffer.length && buffer[tagEnd] !== '>') {
        tagEnd++;
      }

      if (tagEnd >= buffer.length) {
        // Incomplete opening tag
        return { element: null, endPosition: startPos };
      }

      tagEnd++; // Include the '>'

      const openingTag = buffer.substring(startPos, tagEnd);

      // Check if it's a self-closing tag
      if (openingTag.endsWith('/>') || openingTag.includes(' />')) {
        return { element: openingTag, endPosition: tagEnd };
      }

      // Extract tag name for finding closing tag
      const tagNameMatch = openingTag.match(/<([a-zA-Z][a-zA-Z0-9]*)/);
      if (!tagNameMatch) {
        return { element: null, endPosition: startPos };
      }

      const tagName = tagNameMatch[1].toLowerCase();

      // Special handling for void elements (no closing tag needed)
      const voidElements = ['area', 'base', 'br', 'col', 'embed', 'hr', 'img', 'input', 'link', 'meta', 'param', 'source', 'track', 'wbr'];
      if (voidElements.includes(tagName)) {
        return { element: openingTag, endPosition: tagEnd };
      }

      // Find the matching closing tag
      const closingTag = `</${tagName}>`;
      let depth = 1;
      let searchPos = tagEnd;

      while (searchPos < buffer.length && depth > 0) {
        const nextOpenTag = buffer.indexOf(`<${tagName}`, searchPos);
        const nextCloseTag = buffer.indexOf(closingTag, searchPos);

        if (nextCloseTag === -1) {
          // No closing tag found
          return { element: null, endPosition: startPos };
        }

        if (nextOpenTag !== -1 && nextOpenTag < nextCloseTag) {
          // Found another opening tag before the closing tag
          depth++;
          searchPos = nextOpenTag + tagName.length + 1;
        } else {
          // Found a closing tag
          depth--;
          if (depth === 0) {
            const endPos = nextCloseTag + closingTag.length;
            const element = buffer.substring(startPos, endPos);
            return { element, endPosition: endPos };
          }
          searchPos = nextCloseTag + closingTag.length;
        }
      }

      // Incomplete element
      return { element: null, endPosition: startPos };
    };

    // Process any remaining content in buffer when stream ends
    const processRemainingBuffer = async () => {
      const remainingContent = bufferRef.current.trim();
      if (remainingContent && !animationRef.current.cancel) {
        // Try to parse remaining content as a complete element
        // If it's not complete, we'll still try to render it
        await renderSingleElement(remainingContent);
      }
    };

    // Render a single HTML element with animation
    const renderSingleElement = async (elementHtml: string): Promise<void> => {
      if (animationRef.current.cancel || !container) return;

      try {
        // Parse the HTML element using DOMParser
        const parser = new DOMParser();
        const doc = parser.parseFromString(elementHtml, 'text/html');
        const bodyContent = doc.body;

        if (!bodyContent || !bodyContent.firstChild) return;

        // Get the first child element
        const element = bodyContent.firstChild.cloneNode(true) as Node;

        // If it's an element node, add animation classes
        if (element.nodeType === Node.ELEMENT_NODE) {
          const htmlElement = element as HTMLElement;

          // Add initial animation classes (hidden and slightly offset)
          htmlElement.classList.add(
            'opacity-0',
            'translate-y-2',
            'transition-opacity',
            'transition-transform',
            'duration-300',
            'ease-out'
          );
        }

        // Append to container (element is still invisible due to opacity-0)
        container.appendChild(element);

        // Trigger animation on next frame
        if (element.nodeType === Node.ELEMENT_NODE) {
          const htmlElement = element as HTMLElement;

          requestAnimationFrame(() => {
            if (animationRef.current.cancel) return;

            // Trigger the fade-in and slide-up animation
            htmlElement.classList.remove('opacity-0', 'translate-y-2');
            htmlElement.classList.add('opacity-100', 'translate-y-0');
          });
        }

        console.log('🎬 Rendered streaming element:', elementHtml.substring(0, 50) + '...');

      } catch (error) {
        console.error('🎬 Error rendering element:', error, elementHtml);
      }
    };

    // Start processing the stream
    processStream();

    // Cleanup function to cancel streaming if component unmounts
    return () => {
      animationRef.current.cancel = true;
      if (readerRef.current) {
        readerRef.current.cancel().catch(console.error);
        readerRef.current = null;
      }
    };
  }, [stream, delayPerElement, onAnimationComplete, onError]);

  return (
    <div
      ref={containerRef}
      className="streaming-html-container w-full h-full"
      style={{ minHeight: '100%' }}
    />
  );
};

/**
 * Utility function to create a ReadableStream from HTML string for testing StreamingHtmlRenderer
 * This simulates streaming HTML content by chunking it and sending it progressively
 *
 * Example usage:
 * ```tsx
 * const htmlContent = '<div>Hello</div><p>World</p>';
 * const stream = createHtmlStream(htmlContent, 50, 100);
 *
 * return <StreamingHtmlRenderer stream={stream} />;
 * ```
 *
 * For real streaming from fetch():
 * ```tsx
 * const response = await fetch('/api/generate-html');
 * const stream = response.body;
 *
 * return <StreamingHtmlRenderer stream={stream} />;
 * ```
 */
export const createHtmlStream = (html: string, chunkSize: number = 100, delayMs: number = 50): ReadableStream<Uint8Array> => {
  const encoder = new TextEncoder();
  let position = 0;

  return new ReadableStream({
    start() {
      console.log('🎬 Starting HTML stream simulation');
    },

    pull(controller) {
      if (position >= html.length) {
        console.log('🎬 HTML stream completed');
        controller.close();
        return;
      }

      // Get the next chunk
      const chunk = html.substring(position, position + chunkSize);
      position += chunkSize;

      // Encode and enqueue the chunk
      const encodedChunk = encoder.encode(chunk);
      controller.enqueue(encodedChunk);

      console.log(`🎬 Streamed chunk: ${chunk.length} characters`);

      // Add delay to simulate network latency
      if (delayMs > 0) {
        return new Promise(resolve => setTimeout(resolve, delayMs));
      }
    },

    cancel() {
      console.log('🎬 HTML stream cancelled');
    }
  });
};

export default AnimatedHtmlRenderer;
export { StreamingHtmlRenderer };
