import React, { useEffect, useRef } from 'react';

interface AnimatedHtmlRendererProps {
  html: string;
  delayPerElement?: number;
  onAnimationComplete?: () => void;
}

/**
 * AnimatedHtmlRenderer - Provides smooth, jitter-free, progressive element-by-element rendering
 * 
 * This component emulates Readdy.ai's smooth rendering by:
 * 1. Parsing HTML string into individual DOM nodes
 * 2. Rendering each top-level node one at a time with smooth animations
 * 3. Using Tailwind utility classes for fade-in and slide-up effects
 * 4. Avoiding layout jank by using manual DOM manipulation instead of dangerouslySetInnerHTML
 */
const AnimatedHtmlRenderer: React.FC<AnimatedHtmlRendererProps> = ({ 
  html, 
  delayPerElement = 75,
  onAnimationComplete
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<{ cancel: boolean }>({ cancel: false });

  useEffect(() => {
    const container = containerRef.current;
    if (!container || !html.trim()) return;

    // Reset animation state
    animationRef.current = { cancel: false };

    // Clear existing content
    container.innerHTML = '';

    // Parse HTML using DOMParser for safe parsing
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');
    const bodyContent = doc.body;

    if (!bodyContent) return;

    // Get all top-level nodes from the parsed HTML
    const topLevelNodes = Array.from(bodyContent.childNodes).filter(node => {
      // Filter out empty text nodes and comments
      return node.nodeType === Node.ELEMENT_NODE || 
             (node.nodeType === Node.TEXT_NODE && node.textContent?.trim());
    });

    console.log(`🎬 Starting animated rendering of ${topLevelNodes.length} elements`);

    // Animate each top-level node progressively
    const animateNodes = async () => {
      for (let i = 0; i < topLevelNodes.length; i++) {
        // Check if animation was cancelled (component unmounted)
        if (animationRef.current.cancel) {
          console.log('🎬 Animation cancelled');
          return;
        }

        const node = topLevelNodes[i];
        
        // Clone the node to avoid moving it from the original document
        const clonedNode = node.cloneNode(true) as Node;
        
        // If it's an element, add initial animation classes for smooth entry
        if (clonedNode.nodeType === Node.ELEMENT_NODE) {
          const element = clonedNode as HTMLElement;
          
          // Add Tailwind classes for initial state (hidden and slightly offset)
          element.classList.add(
            'opacity-0', 
            'translate-y-2', 
            'transition-opacity', 
            'transition-transform', 
            'duration-300', 
            'ease-out'
          );
        }

        // Append to container (element is still invisible due to opacity-0)
        container.appendChild(clonedNode);

        // Trigger animation on next frame to ensure element is in DOM
        if (clonedNode.nodeType === Node.ELEMENT_NODE) {
          const element = clonedNode as HTMLElement;
          
          // Use requestAnimationFrame to ensure the element is properly rendered
          requestAnimationFrame(() => {
            if (animationRef.current.cancel) return;
            
            // Trigger the fade-in and slide-up animation
            element.classList.remove('opacity-0', 'translate-y-2');
            element.classList.add('opacity-100', 'translate-y-0');
          });
        }

        // Wait before processing next element (creates the progressive effect)
        if (i < topLevelNodes.length - 1) {
          await new Promise(resolve => setTimeout(resolve, delayPerElement));
        }
      }

      console.log('🎬 Animation completed');
      
      // Call completion callback if provided
      if (onAnimationComplete) {
        onAnimationComplete();
      }
    };

    // Start the animation sequence
    animateNodes().catch(error => {
      console.error('🎬 Animation error:', error);
    });

    // Cleanup function to cancel animation if component unmounts
    return () => {
      animationRef.current.cancel = true;
    };
  }, [html, delayPerElement, onAnimationComplete]);

  return (
    <div 
      ref={containerRef} 
      className="animated-html-container w-full h-full"
      style={{ minHeight: '100%' }}
    />
  );
};

export default AnimatedHtmlRenderer;
