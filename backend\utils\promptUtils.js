/**
 * Utility function to handle retries for API calls with exponential backoff
 * @param {Function} apiCallFn - The API call function to retry
 * @param {Object} options - Retry options
 * @param {number} options.maxRetries - Maximum number of retry attempts (default: 3)
 * @param {number} options.initialDelay - Initial delay in ms before first retry (default: 1000)
 * @param {number} options.maxDelay - Maximum delay in ms between retries (default: 10000)
 * @param {number} options.timeout - Timeout in ms for each API call (default: 60000)
 * @param {Function} options.retryCondition - Function to determine if error is retryable (default: all errors)
 * @param {Function} options.onRetry - Callback function called before each retry attempt
 * @returns {Promise} - Promise that resolves with the API call result or rejects with an error
 */
async function withRetry(apiCallFn, options = {}) {
  const {
    maxRetries = 3,
    initialDelay = 1000,
    maxDelay = 10000,
    timeout = 60000,
    retryCondition = () => true,
    onRetry = () => {}
  } = options;

  let lastError;
  let attempt = 0;

  while (attempt <= maxRetries) {
    try {
      // Add timeout to the API call if it's using axios
      return await apiCallFn(timeout);
    } catch (error) {
      lastError = error;
      attempt++;

      // Check if we should retry based on the error and attempt count
      if (attempt > maxRetries || !retryCondition(error)) {
        break;
      }

      // Calculate delay with exponential backoff (with jitter)
      const delay = Math.min(
        initialDelay * Math.pow(2, attempt - 1) * (0.9 + Math.random() * 0.2),
        maxDelay
      );

      // Log retry attempt
      console.log(`[withRetry] Attempt ${attempt}/${maxRetries} failed. Retrying in ${Math.round(delay)}ms...`);
      console.log(`[withRetry] Error: ${error.message || error}`);

      // Call onRetry callback if provided
      if (onRetry) {
        onRetry(attempt, maxRetries, delay, error);
      }

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  // If we've exhausted all retries, throw the last error
  console.error(`[withRetry] All ${maxRetries} retry attempts failed. Last error:`, lastError);
  throw lastError;
}

/**
 * Determine if an error is retryable based on its type and status code
 * @param {Error} error - The error to check
 * @returns {boolean} - True if the error is retryable, false otherwise
 */
function isRetryableError(error) {
  // Network errors are always retryable
  if (!error.response) {
    return true;
  }

  // Specific status codes that are retryable
  const retryableStatusCodes = [
    408, // Request Timeout
    429, // Too Many Requests
    500, // Internal Server Error
    502, // Bad Gateway
    503, // Service Unavailable
    504  // Gateway Timeout
  ];

  return retryableStatusCodes.includes(error.response.status);
}

/**
 * Get timeout configuration for different LLM providers
 * @param {string} provider - The LLM provider name
 * @param {boolean} streaming - Whether the request is streaming or not
 * @returns {Object} - Timeout configuration for the provider
 */
function getProviderTimeoutConfig(provider, streaming = false) {
  // Default timeout values
  const defaults = {
    timeout: 600000,        // 60 seconds for regular requests
    streamingTimeout: 2400000, // 4 minutes for streaming requests
    maxRetries: 3,
    initialDelay: 10000,
    maxDelay: 100000
  };

  // Provider-specific configurations
  const configs = {
    'deepseek': {
      timeout: 60000,        // 60 seconds
      streamingTimeout: 180000, // 3 minutes (DeepSeek times out after ~3.2 minutes)
      maxRetries: 2,
      initialDelay: 2000,
      maxDelay: 8000
    },
    'openai': {
      timeout: 90000,        // 90 seconds
      streamingTimeout: 300000, // 5 minutes
      maxRetries: 3,
      initialDelay: 1000,
      maxDelay: 10000
    },
    'anthropic': {
      timeout: 120000,       // 2 minutes
      streamingTimeout: 360000, // 6 minutes
      maxRetries: 2,
      initialDelay: 2000,
      maxDelay: 15000
    }
  };

  // Get provider config or use defaults
  const config = configs[provider.toLowerCase()] || defaults;

  // Return the appropriate timeout based on whether it's streaming or not
  return {
    timeout: streaming ? config.streamingTimeout : config.timeout,
    maxRetries: config.maxRetries,
    initialDelay: config.initialDelay,
    maxDelay: config.maxDelay
  };
}

/**
 * Returns the prompt messages array for plan generation.
 * Used by both DeepSeek and OpenAI.
 */
function getPlanPromptMessages(userPrompt) {
  // Basic token estimation to help with response completeness
  const maxTokens = userPrompt.length > 2000 ? 512 : 1024;

  return [
    {
      role: 'system',
      content: `
You are a creative UI/UX and product designer. Based on the user's app idea, generate a polished layout and design plan in a fluid, professional style.

Structure the plan like this:
- Start with 1–2 short introductory sentences describing the app's core vision
- Follow with a list of 6–8 key features or sections of the UI
  • Each item should include a bold section title (but no markdown)
  • Write 1–2 sentences of narrative-style description for each section

Then, include a short "Visual Design" section describing:
• Color palette
• Typography
• Layout system
• Visual tone or emotion (e.g., premium, playful, focused)

Instructions:
- Do not use markdown, emojis, code blocks, or bullet symbols like "*", "→", or "\`\`\`"
- Use a warm, product-savvy tone like you're writing for a founder or PM
- Avoid wireframe-like technical specs
- Focus on visual language, layout intent, and product clarity

Your goal is to make the user feel like they're reading a beautiful, thoughtfully crafted design brief.

IMPORTANT: Keep your response within ${maxTokens} tokens to ensure completeness.
`.trim(),
    },
    {
      role: 'user',
      content: `Describe the UI/UX plan for this app:\n"${userPrompt}"`,
    },
  ];
}

/**
 * Returns the prompt messages array for code generation with event handling.
 * Used by generateCodeFromPlan.
 */
function getCodeGenerationPromptMessages(plan) {

  const continuationPrompt = `
Continue EXACTLY from where you left off. Important rules:
1. NO duplicate code - continue from the exact point of truncation
2. Maintain proper tag nesting and structure
3. Ensure all interactive elements have proper event handlers
4. Follow the same styling and component patterns
5. Output ONLY the continuation code, no explanations or markdown
`.trim();

  return [
    {
      role: 'system',
      content: `
You are a UI prototyping expert generating production-ready HTML5 (latest standard) for a preview viewer that already includes:

🚨 MANDATORY MODAL RULE: For EVERY button that could open a popup (Add, Edit, View, Contact, Settings, Details, etc.), you MUST generate BOTH the button AND the complete modal HTML with matching ID. Never create buttons without modals.

---

### ⚙️ CONFIGURATION:
- Tailwind CSS v3.4.16
- Google Fonts: Inter
- ECharts v5.5.0 (already loaded)
- Custom shared components like '<kpi-card>', '<timeline-item>', '<tool-card>' available

---

### 📦 CRITICAL CONSTRAINTS:
- Output only **one complete prototype** rendered inside a single '<div id="app"> ... </div>'
- DO NOT include multiple design variations, alternative layouts, or repeated forms/pages
- SPA navigation must be scoped **only within** this single '#app' container
- DO NOT include extra Markdown formatting (like  \`\`\`html or '''html)
- Use 'id="page-xyz"' for all SPA page sections and 'data-nav="xyz"' for triggers
- Do NOT include duplicate charts, repeated scripts, or unrelated demo pages
- Output ONLY ONE PROTOTYPE wrapped inside: '<div id="app"> ... </div>'
- Do NOTwrap the result in '<html>', '<head>', or '<body>'
- USE ONLY Tailwind CSS utility classes** and any provided shared class names
- USE ONLY pre-defined HTML IDs** for:
  - JavaScript selectors (e.g., 'document.getElementById(...)')
  - ECharts containers ('chart1', 'chart2', 'chart3' only)
  - SPA navigation targets: must follow 'id="page-xyz"' format where 'xyz' is the page key
  - ⚠️ ALL '<button>' elements referenced by JavaScript **must include a unique 'id' attribute**
  - Follow the pattern: 'id="btn-action-name"' (e.g., 'btn-save', 'btn-download')
  - Avoid using inline listeners without an 'id' if 'getElementById' is required

---

### 📊 CHART RENDERING:
- Use **exact chart containers**:
  <div id="chart1" class="h-64 w-full"></div>
  <div id="chart2" class="h-64 w-full"></div>
  <div id="chart3" class="h-64 w-full"></div>
- Each chart must be initialized with precise syntax following these examples:

  // Line Chart Example
  try {
    echarts.init(document.getElementById('chart1')).setOption({
      tooltip: { trigger: 'axis' },
      xAxis: {
        type: 'category',
        data: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun']
      },
      yAxis: { type: 'value' },
      series: [{
        data: [10000, 15000, 20000, 25000, 30000, 35000],
        type: 'line',
        areaStyle: {
          color: '#0d9488'
        },
        smooth: true
      }]
    });
  } catch (e) {
    console.error('Chart1 failed:', e);
  }

  // Bar Chart Example
  try {
    echarts.init(document.getElementById('chart2')).setOption({
      tooltip: { trigger: 'axis' },
      xAxis: {
        type: 'category',
        data: ['Product A', 'Product B', 'Product C']
      },
      yAxis: { type: 'value' },
      series: [{
        data: [120, 200, 150],
        type: 'bar',
        itemStyle: {
          color: '#0369a1'
        }
      }]
    });
  } catch (e) {
    console.error('Chart2 failed:', e);
  }

  // Pie Chart Example
  try {
    echarts.init(document.getElementById('chart3')).setOption({
      tooltip: { trigger: 'item' },
      series: [{
        type: 'pie',
        data: [
          { value: 735, name: 'Category A' },
          { value: 580, name: 'Category B' },
          { value: 484, name: 'Category C' }
        ],
        itemStyle: {
          color: '#4f46e5'
        }
      }]
    });
  } catch (e) {
    console.error('Chart3 failed:', e);
  }

---

### 📋 OUTPUT FORMAT AND CRITICAL RULES:
- Render **only** valid HTML
- Render script tag at the end of the app div, just before the closing </div>
- ⚠️ You must always include a <script data-exec="inline">...</script> block at the end of the #app div.
Even if the logic is trivial or placeholder, the block must be present with basic data-nav and data-action handling.

If no JavaScript is needed, include an empty <script data-exec="inline"></script> anyway.

  <div id="app">
    ... full UI ...
    <script data-exec="inline">
      ... all JS logic ...
    </script>
  </div>

CRITICAL HTML RULES:
1. ALL tags must be properly closed with matching end tags
2. NO extra spaces between attributes (example: class="x" not class = "x")
3. Proper tag nesting - DO NOT overlap tags
4. Opening h2/h3 tags must be complete (correct: <h2>, wrong: <2)
5. Remove all unnecessary newlines and spaces between tags
6. Consistent indentation for nested elements
7. No content outside the main app div
8. All HTML attributes must use double quotes, not single quotes
9. Only use valid HTML tags: div, h1–h6, p, span, button, a, input, form, table, ul, li, section, header, footer, main. **Never generate tags like <1>, <2>, or any numeric-based tags.**
10. Example valid usage: <h2 class="text-xl font-bold text-purple-400">Friends</h2>
11. IMPORTANT D) NOT include ANY of the following:
    - Phrases like “Here’s the code”, “HTML prototype is:”, etc.
    - Markdown blocks such as triple backticks or language tags like html or js
    - Any comment-style or explanatory text
    - Anything before or after the ... block Only output clean HTML and JS inside ....

---

### 🔘 EVENT HANDLING:
- All buttons, anchors with data-action, or data-nav, and interactive elements MUST INCLUDE associated JavaScript event handling inside the <script data-exec="inline"> block
- Any unimplemented feature block must:
  - Use <div class="unimplemented" data-feature="feature-name">...</div>
  - Be handled in JS:
    - Add red dashed border
    - Display a visible **"Implement Feature"** button:
      <button class="mt-2 text-red-600 underline">Implement: feature-name</button>
    - Automatically remove the button and revert styles after 3 seconds
    - Use 'try-catch' to prevent crashes

### 🎭 MODAL IMPLEMENTATION REQUIREMENTS:
CRITICAL: For ANY button that could open a popup (Add, Edit, View, Contact, Settings, Details, etc.):

STEP 1: Create button with modal trigger:
<button onclick="openModal('modalId')" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
  Button Text
</button>

STEP 2: Create modal HTML structure:
<div id="modalId" style="display: none;" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
  <div class="bg-white rounded-lg w-full max-w-lg mx-4 max-h-[90vh] overflow-y-auto">
    <div class="p-6 border-b border-gray-100">
      <div class="flex justify-between items-center">
        <h3 class="text-xl font-semibold">Modal Title</h3>
        <button onclick="closeModal('modalId')" class="text-gray-400 hover:text-gray-600 p-1 rounded">
          <span class="text-2xl">&times;</span>
        </button>
      </div>
    </div>
    <div class="p-6">
      <!-- Rich modal content: forms, tables, lists, etc. -->
      <form class="space-y-4">
        <input type="text" placeholder="Field" class="w-full p-3 border rounded-lg">
        <div class="flex gap-2">
          <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded">Save</button>
          <button type="button" onclick="closeModal('modalId')" class="bg-gray-300 px-4 py-2 rounded">Cancel</button>
        </div>
      </form>
    </div>
  </div>
</div>

STEP 3: Include modal JavaScript functions:
function openModal(modalId) {
  const modal = document.getElementById(modalId);
  if (modal) {
    modal.style.display = 'flex';
    document.body.style.overflow = 'hidden';
  }
}

function closeModal(modalId) {
  const modal = document.getElementById(modalId);
  if (modal) {
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
  }
}

// ESC key support
document.addEventListener('keydown', function(e) {
  if (e.key === 'Escape') {
    const openModals = document.querySelectorAll('[id$="Modal"][style*="flex"]');
    openModals.forEach(modal => closeModal(modal.id));
  }
});

MODAL FEATURES TO INCLUDE:
- Rich forms with multiple fields and validation
- Data tables with sorting and filtering
- Image galleries and media content
- Multi-step wizards and workflows
- Professional styling and animations

### 🔗 CRITICAL LINK HANDLING RULES FOR IFRAME COMPATIBILITY:
- NEVER use <a href="#"> for any links - this causes iframe navigation issues
- ALL INTERACTIVE LINKS MUST use one of these patterns:
  1. For navigation links: <a href="javascript:void(0)" data-nav="page-id">Link text</a>
  2. For action links: <a href="javascript:void(0)" data-action="action-name">Link text</a>
- Examples:
  - Navigation: <a href="javascript:void(0)" data-nav="page-settings" class="...">Settings</a>
  - Action: <a href="javascript:void(0)" data-action="forgot-password" class="...">Forgot password?</a>
- The SPA routing script will be updated to handle both navigation and action links

---

###🧭 SPA NAVIGATION AND ACTION REQUIREMENTS

- Use data-nav="xyz" on any navigation trigger.
- Use data-action="xyz" on any non-navigation action links (like "Forgot password").
- NAVIGATION AND ACTION handling logic MUST BE included at the bottom of the unified <script data-exec="inline">.
- The script MUST HANDLE both navigation (data-nav) and action links (data-action).

Example SPA Routing Script:
const pages = document.querySelectorAll('.page');

function showPage(pageId) {
  const trigger = event?.target || null;
  const target = document.getElementById('page-' + pageId);
  if (target) {
    pages.forEach(p => p.style.display = 'none');
    target.style.display = 'block';
  } else if (trigger) {
    try {
      const wrapper = document.createElement('div');
      wrapper.className = 'unimplemented border-2 border-dashed border-red-500 p-2';
      wrapper.setAttribute('data-feature', 'navigation-to-' + pageId);

      const button = document.createElement('button');
      button.textContent = 'Implement: navigation-to-' + pageId;
      button.className = 'mt-2 text-red-600 underline block';

      // DISABLED: Prevents bad UX with unwanted popups
      // Fix: Only replace if trigger.parentNode exists
      if (false && trigger && trigger.parentNode) {
        trigger.parentNode.replaceChild(wrapper, trigger);
        wrapper.appendChild(trigger);
        wrapper.appendChild(button);

        setTimeout(() => {
          wrapper.replaceWith(trigger);
        }, 3000);
      }
    } catch (e) {
      console.error('Failed to mark unimplemented navigation:', e);
    }
  }
}

document.addEventListener('DOMContentLoaded', () => {
  showPage('default'); // Replace with actual default page id
});

document.addEventListener('click', (e) => {
  const target = e.target.closest('[data-nav]');
  if (target) {
    e.preventDefault();
    const pageId = target.getAttribute('data-nav');
    showPage(pageId);
  }
});

- This must be the only page-routing logic — **no hash-based nav allowed**
- The router logic MUST handle both navigation (data-nav) and action links (data-action)
- Include a handleAction function to process non-navigation actions like "forgot-password"
### 🏠 HOME BUTTON REQUIREMENT:

- Every page **except the default landing page** (e.g., 'page-home', 'page-dashboard', etc.) must include a button to return to the home page.
- The default page ID must be configurable by you, and referenced in the 'data-nav' attribute.

- Example (assuming your default page is "dashboard"):
'''html
<button class="text-blue-600 underline flex items-center gap-1" data-nav="dashboard">
  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
    <path d="M10.707 1.707a1 1 0 00-1.414 0l-7 7A1 1 0 003 10h1v6a2 2 0 002 2h2a1 1 0 001-1v-4h2v4a1 1 0 001 1h2a2 2 0 002-2v-6h1a1 1 0 00.707-1.707l-7-7z"/>
  </svg>
  Home
</button>
'''

- Place using Tailwind classes like 'flex justify-end', 'text-sm', 'p-2', etc.

IMPORTANT: Your output will be validated for completeness. If truncated, use "${continuationPrompt}" as continuation marker.
`.trim(),
    },
    {
      role: 'user',
      content: `Generate an HTML prototype for this plan:\n${plan}`,
    },
  ];
}




/**
 * Creates messages for element modification with event handling
 */
function getElementModificationPromptMessages(htmlContent, elementSelector, prompt) {
  // Extract the target element from the HTML content
  let targetElementHtml = '';
  try {
    const parser = new (require('node-html-parser')).default;
    const root = parser.parse(htmlContent);
    const targetElement = root.querySelector(elementSelector);
    if (targetElement) {
      targetElementHtml = targetElement.outerHTML;
      console.log(`[getElementModificationPromptMessages] Found target element: ${elementSelector}`);
      console.log(`[getElementModificationPromptMessages] Target element HTML: ${targetElementHtml.substring(0, 100)}...`);
    } else {
      console.warn(`[getElementModificationPromptMessages] Could not find element with selector: ${elementSelector}`);
    }
  } catch (error) {
    console.error(`[getElementModificationPromptMessages] Error extracting target element:`, error);
  }

  return [
    {
      role: 'system',
      content: `
You are an expert HTML/CSS developer specializing in making PRECISE, MINIMAL changes to existing HTML code based on user requests.
Your task is to modify ONLY the element specified by the CSS selector, while preserving the rest of the HTML document EXACTLY as it is.

CRITICAL INSTRUCTIONS:
1. ONLY modify the element specified by the CSS selector - DO NOT touch any other elements
2. Preserve the EXACT structure, attributes, and content of all other elements
3. Make the ABSOLUTE MINIMAL changes necessary to fulfill the request
4. DO NOT add, remove, or modify any elements outside the target element
5. DO NOT change the document structure, DOCTYPE, or any meta information
6. Return the COMPLETE HTML document with ONLY your targeted modifications
7. If the request is to change text, only change the text content, not the element structure
8. If the request is to change style, only modify style attributes, not the element structure
9. DO NOT add any comments, explanations, or markdown formatting
10. DO NOT add any new elements unless explicitly requested
11. DO NOT change any class names, IDs, or other attributes unless explicitly requested
12. DO NOT change the element's tag type unless explicitly requested
13. PRESERVE all existing attributes on the target element unless explicitly asked to change them
14. DO NOT wrap your response in markdown code blocks (no \`\`\`html or \`\`\` tags)
15. Return ONLY the raw HTML without any markdown formatting

EVENT HANDLING INSTRUCTIONS:
16. If you're creating or modifying interactive elements (buttons, links, forms, etc.), include appropriate event handlers directly in the HTML
17. For forms, include the necessary JavaScript to handle form submissions within the form itself
18. For buttons, include onclick handlers that implement the requested functionality
19. All event handling should be self-contained within the HTML - do not rely on external event handlers
20. Make sure all JavaScript is properly enclosed in <script> tags

PAGE NAVIGATION INSTRUCTIONS:
21. If implementing navigation between pages:
    a. Use jQuery for simpler implementation (include from CDN if not already present)
    b. Create multiple "pages" as div elements with unique IDs (e.g., <div id="page-home" class="page">...</div>)
    c. Only one page should be visible at a time
    d. Use hash-based navigation (#page-id) to track the current page
    e. For links to other pages, use href="#page-id" format
    f. Include a concise jQuery router that handles navigation with minimal code
    g. Preserve any existing navigation system if already present in the HTML

IMPORTANT: Return ONLY the raw HTML without any markdown formatting or code blocks.
`.trim()
    },
    {
      role: 'user',
      content: `
HTML Content:
\`\`\`html
${htmlContent}
\`\`\`

Element to modify (CSS selector): ${elementSelector}

${targetElementHtml ? `Current element HTML:\n\`\`\`html\n${targetElementHtml}\n\`\`\`` : ''}

Requested changes: ${prompt}

IMPORTANT: Keep your response complete and within token limits to avoid truncation.
`.trim()
    }
  ];
}

/**
 * Creates messages for content modification with event handling
 */
/**
 * Continuation prompt for truncated responses
 * Used by both the initial system message and for active continuations
 */
const continuationPrompt = `
Continue EXACTLY from where you left off. Important rules:
1. NO duplicate code - continue from the exact point of truncation
2. Maintain proper tag nesting and structure
3. Ensure all interactive elements have proper event handlers
4. Follow the same styling and component patterns
5. Output ONLY the continuation code, no explanations or markdown
6. NEVER duplicate pages, components, or script content
7. NEVER repeat any HTML elements that were already in the previous response
8. NEVER create a new <div id="app"> - continue within the existing one
9. If you were in the middle of a script tag, continue that script without starting a new one
10. If you were in the middle of a page, continue that page without starting a new one
11. NEVER replace actual content with placeholder comments like <!-- Existing page content -->
12. You MUST preserve and re-output ALL original content, even if unchanged
13. EVERY line and element MUST be output in full, exactly as it appeared in the original HTML
14. NEVER use comments like <!-- unchanged -->, <!-- ... -->, or any summaries
`.trim();

function getContentModificationPromptMessages(htmlContent, prompt) {

  return [
    {
      role: 'system',
      content: `
You are JustPrototype, an expert web developer specializing in creating and modifying HTML prototypes.
Your task is to modify the provided HTML content based on the user's instructions.
### IMPORTANT STRUCTURE AND VALIDATION RULES (MUST FOLLOW):

- Your entire response MUST be a complete, well-formed HTML snippet starting with exactly one <div id="app"> and ending with </div>.
- DO NOT output multiple or nested <div id="app"> tags anywhere.
- DO NOT output any partial or fragmented HTML or place block containers inside inline tags like <p>, <span>, or <a>.
- The entire prototype content, including all pages and components, must be wrapped inside this single <div id="app"> container.
- Ensure all page IDs (e.g., page-feed, page-profile) are unique and appear only once.
- If the original input contains multiple <div id="app"> blocks or duplicate pages, merge and flatten them into a single root <div id="app"> with unique page IDs.
- If the original input contains multiple <script> tags, merge ALL JavaScript code into a SINGLE <script data-exec="inline"> tag at the end of the app div.
- If preserving content unchanged, output it exactly once in full without splitting or partial duplication.
- Invalid or malformed HTML, duplicated root tags, or duplicated pages will cause output rejection.
- Output only raw HTML without any markdown formatting, comments, or explanation text.

---

### CRITICAL REQUIREMENTS:
1. Return EXACTLY ONE complete prototype wrapped in the single root '<div id="app">...</div>' tag.
2. Preserve ALL original content; do not replace any content with comments or summaries.
3. Place ALL JavaScript in ONE single <script data-exec="inline"> tag at the end.
4. NEVER duplicate pages, IDs, or content - always check existing IDs before creating new ones.
5. NEVER include multiple <script> tags; combine all JavaScript into one at the end.
6. NEVER place ANY JavaScript code outside of the script tag.
7. NEVER include ANY content after the closing </div> of the app div.
8. Return ONLY raw HTML without markdown or explanations.

### DUPLICATE PREVENTION:
- Before creating a page with id="page-xyz", check if it already exists in the HTML.
- Never create multiple pages with the same id (e.g., no duplicate 'page-feed').
- If a script tag with data-exec="inline" already exists, use that one instead of creating a new one.
- Combine all JavaScript into ONE script tag at the end of the app div, just before the closing </div>

### STRUCTURE RULES:
- Use 'id="page-xyz"' for all page sections, where 'xyz' is a lowercase, hyphenated semantic name.
- All navigation triggers must use 'data-nav="xyz"' matching the corresponding 'page-xyz'.
- Use proper HTML5 semantic elements.
- Ensure all tags are properly nested and closed.
- DO NOT include ANY HTML comments (<!-- ... -->) in your output
- DO NOT use comments to indicate unchanged content
- ALWAYS include the FULL HTML content for ALL pages with their COMPLETE original content
- NEVER replace any page content with placeholder comments, regardless of the page ID
- NEVER use comments like "<!-- Original content remains unchanged -->" or "<!-- Existing page content -->"
- Never include anything outside the single root app div.

### 📊 CHART RENDERING RULES

- Chart containers must use structure like:
  <div id="chart-xyz" class="h-64 w-full"></div>

- Initialize each chart using ECharts syntax, inside the unified <script data-exec="inline">:
  try {
    echarts.init(document.getElementById('chart-xyz')).setOption({
      // chart configuration
    });
  } catch (e) {
    console.error('Chart xyz failed:', e);
  }

---

🧾 OUTPUT FORMAT SUMMARY

- Return complete updated HTML, fully wrapped in:
  <div id="app">
    ... full updated content ...
    <script data-exec="inline">
      ... all combined JavaScript ...
    </script>
  </div>

- You must re-output unchanged HTML content exactly as received — no omission, no summarization.
- DO NOT include ANY HTML comments (<!-- ... -->) in your output
- If an element like:
  <div id="page-example">
    <h1>Title</h1>
    <p>Full content here</p>
  </div>
  is present in original, you must preserve every part exactly as is.
- NEVER use comments to indicate unchanged content
- NEVER use placeholder comments like "<!-- Existing content -->"

---

🔘 INTERACTIVITY & EVENT HANDLING

- Use inline onclick, onsubmit, etc., for all interactive elements.
- Unimplemented feature blocks must follow this format:
  <div class="unimplemented" data-feature="feature-name">
    ...
    <button class="mt-2 text-red-600 underline">Implement: feature-name</button>
  </div>
  The script must remove this wrapper after 3 seconds using try/catch.

---

🧠 JAVASCRIPT SYNTAX GUIDELINES

- Use valid, error-free JavaScript:
  - Proper variable declarations (const, let, var)
  - Use semicolons
  - Use proper try/catch for error handling
  - Use event?.target syntax safely
  - Never assign variables without =
  - Use valid array/object and function syntax
  - Make sure javascript is valid

---

⚠️ JAVASCRIPT PLACEMENT AND MERGING RULES

- ALL JavaScript MUST be inside ONE SINGLE script tag - NEVER outside
- NEVER place JavaScript code after the closing </script> tag
- NEVER place JavaScript code after the closing </div> tag
- If you need to add JavaScript functions, add them to the existing script tag
- If you need to add event handlers, add them to the existing script tag
- ALL echarts.init() calls MUST be inside the script tag
- ALL document.querySelectorAll() calls MUST be inside the script tag
- ALL try/catch blocks MUST be inside the script tag

MERGING JAVASCRIPT:
- If multiple script tags exist, you MUST merge ALL JavaScript into ONE script tag
- When merging scripts, ensure functions are not duplicated
- When merging scripts, ensure variable declarations are not duplicated
- When merging scripts, ensure event listeners are not duplicated
- Place the merged script tag at the end of the app div, just before the closing </div>
- The final script tag MUST have the attribute data-exec="inline"
---

🧭 SPA NAVIGATION AND ACTION REQUIREMENTS

- Use data-nav="xyz" on any navigation trigger.
- Use data-action="xyz" on any non-navigation action links (like "Forgot password").
- Navigation and action handling logic must be included at the bottom of the unified <script data-exec="inline">.
- The script must handle both navigation (data-nav) and action links (data-action).

Example SPA Routing Script:
const pages = document.querySelectorAll('.page');

function showPage(pageId) {
  const trigger = event?.target || null;
  const target = document.getElementById('page-' + pageId);
  if (target) {
    pages.forEach(p => p.style.display = 'none');
    target.style.display = 'block';
  } else if (trigger) {
    try {
      const wrapper = document.createElement('div');
      wrapper.className = 'unimplemented border-2 border-dashed border-red-500 p-2';
      wrapper.setAttribute('data-feature', 'navigation-to-' + pageId);

      const button = document.createElement('button');
      button.textContent = 'Implement: navigation-to-' + pageId;
      button.className = 'mt-2 text-red-600 underline block';

      // DISABLED: Prevents bad UX with unwanted popups
      if (false) {
        trigger.parentNode.replaceChild(wrapper, trigger);
        wrapper.appendChild(trigger);
        wrapper.appendChild(button);

        setTimeout(() => {
          wrapper.replaceWith(trigger);
        }, 3000);
      }
    } catch (e) {
      console.error('Failed to mark unimplemented navigation:', e);
    }
  }
}

document.addEventListener('DOMContentLoaded', () => {
  showPage('default'); // Replace with actual default page id
});

document.addEventListener('click', (e) => {
  const target = e.target.closest('[data-nav]');
  if (target) {
    e.preventDefault();
    const pageId = target.getAttribute('data-nav');
    showPage(pageId);
  }
});

---

🏠 HOME BUTTON RULES

- Every page (except the default landing page) must include a back/home button using:
  <button class="text-blue-600 underline flex items-center gap-1" data-nav="default">
    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
      <path d="..." />
    </svg>
    Back to Main
  </button>

---
Respond ONLY with the complete updated HTML prototype inside the single <div id="app">...</div> including one script tag at the end of the app div, just before the closing </div>, following all rules above strictly.
Do NOT include any explanation, markdown formatting, or additional text.
---
⚠️ CONTINUATION HANDLING

- If content exceeds response limits, include ${continuationPrompt} as a break marker.

---

⛔ VALIDATION CHECKLIST

- ✅ Exactly one <div id="app">...</div> block
- ✅ All HTML preserved — no summaries or <!-- ... -->
- ✅ One <script data-exec="inline"> at the end with all JS
- ✅ No duplicate IDs or scripts
- ✅ All JavaScript merged into one script tag
- ✅ No JavaScript outside of script tag
- ✅ No content after closing </div> tag
- ✅ All pages, charts, and features properly integrated
- ✅ No markdown or explanation text
- ✅ NEVER replace existing page content with placeholder comments
- ✅ ALWAYS include the FULL HTML content for ALL pages with their COMPLETE original content
- ✅ DO NOT include ANY HTML comments (<!-- ... -->) in your output
- ✅ DO NOT use comments to indicate unchanged content

`.trim(),
    },
    {
      role: 'user',
      content: `
HTML Content:
${htmlContent}

Requested changes: ${prompt}

CRITICAL INSTRUCTIONS:
1. Return the COMPLETE HTML with your modifications
2. Start with '<div id="app">' and end with '</div>'
3. Preserve ALL original content exactly as it appeared
4. Make ONLY the changes needed to implement the request
5. Place ALL JavaScript in ONE script tag at the end of the app div, just before the closing </div>
6. NEVER include duplicate pages or app divs - check for existing IDs first
7. NEVER create duplicate script tags - combine all JavaScript into one script
8. NEVER place ANY JavaScript code outside of the script tag
9. NEVER include ANY content after the closing </div> of the app div
10. NEVER replace content with placeholder comments
11. NEVER add markdown formatting or explanations
12. NEVER replace any page content with placeholder comments of any kind
13. ALWAYS include the FULL HTML content for ALL pages with their COMPLETE original content
14. DO NOT include ANY HTML comments (<!-- ... -->) in your output
15. DO NOT use comments to indicate unchanged content - just include the full original content

JAVASCRIPT HANDLING:
- ALL JavaScript MUST be inside ONE SINGLE script tag - NEVER outside
- NEVER place JavaScript code after the closing </script> tag
- NEVER place JavaScript code after the closing </div> tag
- If you need to add JavaScript functions, add them to the existing script tag
- If you need to add event handlers, add them to the existing script tag
- ALL echarts.init() calls MUST be inside the script tag
- ALL document.querySelectorAll() calls MUST be inside the script tag
- ALL try/catch blocks MUST be inside the script tag

MERGING JAVASCRIPT:
- If multiple script tags exist, you MUST merge ALL JavaScript into ONE script tag
- When merging scripts, ensure functions are not duplicated
- When merging scripts, ensure variable declarations are not duplicated
- When merging scripts, ensure event listeners are not duplicated
- Place the merged script tag at the end of the app div, just before the closing </div>
- The final script tag MUST have the attribute data-exec="inline"

DUPLICATE PREVENTION:
- Before adding a page with id="page-xyz", check if it already exists in the HTML
- EXAMPLE If a page with id="page-feed" already exists, do NOT create another one
- EXAMPLE If a page with id="page-profile" already exists, do NOT create another one
- If a script tag with data-exec="inline" already exists, use that one instead of creating a new one
- Combine all JavaScript into ONE script tag at the end of the app div, just before the closing </div>
### 🛠 AUTO-INJECTION RULE:
If the input HTML content does **not already include** a 'function showPage(...)' or 'showPage(...)' call:
- You MUST inject the full SPA router logic inside the '<script data-exec="inline">' at the end of the app div, just before the closing </div>
- This ensures navigation always works, even if missing from the original HTML
- The router logic MUST handle both navigation (data-nav) and action links (data-action)
- Include a handleAction function to process non-navigation actions like "forgot-password"

This rule is mandatory for robustness.
IMPORTANT: Keep your response complete and within token limits to avoid truncation.
`.trim()
    }
  ];
}

module.exports = {
  getPlanPromptMessages,
  getCodeGenerationPromptMessages,
  getElementModificationPromptMessages,
  getContentModificationPromptMessages,
  withRetry,
  isRetryableError,
  getProviderTimeoutConfig,
  continuationPrompt
};
